# Installation Guide - WA Contact Extractor

This guide will help you install and set up the WA Contact Extractor Chrome extension.

## Prerequisites

- Google Chrome browser (version 88 or higher)
- Access to WhatsApp Web (https://web.whatsapp.com)

## Step 1: Download the Extension

1. Download or clone this repository to your computer
2. Extract the files if you downloaded a ZIP archive
3. Make note of the folder location (you'll need it in Step 3)

## Step 2: Generate Icons

The extension requires icon files to work properly. Follow these steps:

1. Open the `generate-icons.html` file in your Chrome browser
2. Click the "Download" button for each icon size (16x16, 32x32, 48x48, 128x128)
3. Save each downloaded icon file in the `icons/` folder with the correct names:
   - `icon16.png`
   - `icon32.png`
   - `icon48.png`
   - `icon128.png`

## Step 3: Install in Chrome

1. Open Google Chrome
2. Navigate to `chrome://extensions/`
3. Enable "Developer mode" by clicking the toggle in the top-right corner
4. Click "Load unpacked" button
5. Select the folder containing the extension files
6. The extension should now appear in your extensions list

## Step 4: Verify Installation

1. Look for the WA Contact Extractor icon in your Chrome toolbar
2. If you don't see it, click the puzzle piece icon (Extensions) and pin the extension
3. Click on the extension icon - you should see the popup interface

## Step 5: First Use

1. Open WhatsApp Web (https://web.whatsapp.com) in a new tab
2. Log in to your WhatsApp account
3. Click on the WA Contact Extractor icon
4. You should see "Ready to extract contacts" status

## Troubleshooting

### Extension Not Loading
- Make sure all files are in the correct locations
- Check that you have all required icon files
- Try refreshing the extensions page and reloading the extension

### "Please open WhatsApp Web first" Error
- Ensure you have WhatsApp Web open in a Chrome tab
- Make sure you're logged in to WhatsApp Web
- Refresh the WhatsApp Web page if needed

### Icons Not Displaying
- Make sure you've generated and saved all icon files
- Check that the icon files are in the `icons/` folder
- Verify the file names match exactly: `icon16.png`, `icon32.png`, etc.

### Extraction Not Working
- Make sure you're on the correct WhatsApp Web page
- For group extraction: Open a group chat first
- For chat extraction: Make sure your chat list is visible
- Try refreshing WhatsApp Web and reloading the extension

## File Structure Check

Your extension folder should look like this:

```
wa-contact-extractor/
├── manifest.json
├── popup.html
├── popup.css
├── popup.js
├── content.js
├── background.js
├── icons/
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
├── generate-icons.html
├── README.md
└── INSTALLATION.md
```

## Permissions

The extension requires these permissions:
- **activeTab**: To interact with the current WhatsApp Web tab
- **storage**: To save extracted contacts locally
- **downloads**: To export contact files
- **host_permissions**: Access to web.whatsapp.com

## Security Notes

- This extension only works locally on your device
- No data is sent to external servers
- All contacts are stored locally in your browser
- The extension only reads information from WhatsApp Web

## Getting Help

If you encounter issues:

1. Check the browser console for error messages (F12 → Console)
2. Try disabling and re-enabling the extension
3. Reload WhatsApp Web and try again
4. Make sure you're using the latest version of Chrome

## Updating the Extension

To update the extension:

1. Download the new version files
2. Replace the old files with the new ones
3. Go to `chrome://extensions/`
4. Click the refresh icon on the WA Contact Extractor extension
5. The extension will reload with the new version

## Uninstalling

To remove the extension:

1. Go to `chrome://extensions/`
2. Find WA Contact Extractor
3. Click "Remove"
4. Confirm the removal

Your extracted contact data will be removed when you uninstall the extension.
