<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .icon {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            margin-top: 10px;
            padding: 8px 16px;
            background: #25D366;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #128C7E;
        }
        .instructions {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h1>WA Contact Extractor - Icon Generator</h1>
        <p>This page generates the required icon files for the Chrome extension. Click the download buttons below to save each icon size.</p>
        <p><strong>Instructions:</strong></p>
        <ol>
            <li>Click each "Download" button to save the icon files</li>
            <li>Save them in the <code>icons/</code> directory with the correct names</li>
            <li>The extension will then be ready to load in Chrome</li>
        </ol>
    </div>

    <div class="icon-container">
        <div class="icon">
            <h3>16x16</h3>
            <canvas id="icon16" width="16" height="16"></canvas>
            <br>
            <button onclick="downloadIcon('icon16', 'icon16.png')">Download</button>
        </div>
        
        <div class="icon">
            <h3>32x32</h3>
            <canvas id="icon32" width="32" height="32"></canvas>
            <br>
            <button onclick="downloadIcon('icon32', 'icon32.png')">Download</button>
        </div>
        
        <div class="icon">
            <h3>48x48</h3>
            <canvas id="icon48" width="48" height="48"></canvas>
            <br>
            <button onclick="downloadIcon('icon48', 'icon48.png')">Download</button>
        </div>
        
        <div class="icon">
            <h3>128x128</h3>
            <canvas id="icon128" width="128" height="128"></canvas>
            <br>
            <button onclick="downloadIcon('icon128', 'icon128.png')">Download</button>
        </div>
    </div>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const center = size / 2;
            const radius = size * 0.4;
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Create gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#25D366');
            gradient.addColorStop(1, '#128C7E');
            
            // Draw background circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(center, center, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw phone icon
            const phoneWidth = size * 0.3;
            const phoneHeight = size * 0.4;
            const phoneX = center - phoneWidth / 2;
            const phoneY = center - phoneHeight / 2;
            
            ctx.fillStyle = 'white';
            ctx.fillRect(phoneX, phoneY, phoneWidth, phoneHeight);
            
            // Draw contact dots
            const dotSize = Math.max(1, size * 0.02);
            const dotSpacing = phoneWidth / 4;
            const startX = phoneX + dotSpacing;
            const startY = phoneY + phoneHeight * 0.3;
            
            ctx.fillStyle = '#128C7E';
            for (let row = 0; row < 3; row++) {
                for (let col = 0; col < 3; col++) {
                    const x = startX + col * dotSpacing;
                    const y = startY + row * dotSpacing;
                    ctx.beginPath();
                    ctx.arc(x, y, dotSize, 0, 2 * Math.PI);
                    ctx.fill();
                }
            }
            
            // Draw export arrow
            if (size >= 32) {
                const arrowX = phoneX + phoneWidth * 0.8;
                const arrowY = phoneY + phoneHeight * 0.8;
                const arrowSize = size * 0.08;
                
                ctx.strokeStyle = 'white';
                ctx.lineWidth = Math.max(1, size * 0.02);
                ctx.lineCap = 'round';
                
                // Arrow shaft
                ctx.beginPath();
                ctx.moveTo(arrowX, arrowY - arrowSize);
                ctx.lineTo(arrowX, arrowY + arrowSize);
                ctx.stroke();
                
                // Arrow head
                ctx.beginPath();
                ctx.moveTo(arrowX - arrowSize/2, arrowY + arrowSize/2);
                ctx.lineTo(arrowX, arrowY + arrowSize);
                ctx.lineTo(arrowX + arrowSize/2, arrowY + arrowSize/2);
                ctx.stroke();
            }
        }
        
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Generate all icons when page loads
        window.onload = function() {
            drawIcon(document.getElementById('icon16'), 16);
            drawIcon(document.getElementById('icon32'), 32);
            drawIcon(document.getElementById('icon48'), 48);
            drawIcon(document.getElementById('icon128'), 128);
        };
    </script>
</body>
</html>
