# Testing Guide - WA Contact Extractor

This guide helps you test the WA Contact Extractor extension to ensure it works correctly.

## Pre-Testing Setup

1. **Install the Extension**
   - Follow the INSTALLATION.md guide
   - Make sure all icon files are generated and in place
   - Verify the extension appears in Chrome's toolbar

2. **Prepare WhatsApp Web**
   - Open https://web.whatsapp.com
   - Log in with your phone
   - Make sure you have some group chats and individual chats

## Test Cases

### Test 1: Basic Extension Loading

**Steps:**
1. Click on the WA Contact Extractor icon
2. Verify the popup opens correctly
3. Check that the interface displays properly

**Expected Results:**
- Popup opens without errors
- All buttons and elements are visible
- Status shows "Ready to extract contacts" or WhatsApp Web instruction

### Test 2: WhatsApp Web Detection

**Steps:**
1. Open the extension on a non-WhatsApp tab
2. Note the error message
3. Switch to WhatsApp Web tab
4. Open the extension again

**Expected Results:**
- Shows "Please open WhatsApp Web first" on non-WhatsApp tabs
- Shows "Ready to extract contacts" on WhatsApp Web tabs

### Test 3: Group Contact Extraction

**Steps:**
1. Open a group chat in WhatsApp Web
2. Click "Extract Group Contacts" in the extension
3. Wait for extraction to complete
4. Check the results

**Expected Results:**
- Status shows "Extracting group contacts..."
- After completion, shows number of contacts extracted
- Contact info panel appears with counts
- Export section becomes visible

### Test 4: Chat Contact Extraction

**Steps:**
1. Make sure your chat list is visible in WhatsApp Web
2. Click "Extract Chat Contacts" in the extension
3. Wait for extraction to complete
4. Check the results

**Expected Results:**
- Status shows "Extracting chat contacts..."
- Extracts contacts from visible chats
- Shows contact counts in the info panel

### Test 5: Export Functionality

**Steps:**
1. Extract some contacts first (Test 3 or 4)
2. Try each export format:
   - Click "Export CSV"
   - Click "Export JSON"
   - Click "Export TXT"
   - Click "Export XLSX"
3. Check downloaded files

**Expected Results:**
- Each export triggers a download
- Files contain the extracted contact data
- File formats are correct and readable

### Test 6: Settings Configuration

**Steps:**
1. Change the settings checkboxes:
   - Uncheck "Include contact names"
   - Uncheck "Include status messages"
   - Uncheck "Remove duplicate contacts"
2. Extract contacts again
3. Check if settings are applied

**Expected Results:**
- Settings affect the extraction results
- Unchecked options are not included in extracted data

### Test 7: Data Persistence

**Steps:**
1. Extract some contacts
2. Close the extension popup
3. Open the extension popup again
4. Check if contacts are still there

**Expected Results:**
- Previously extracted contacts remain visible
- Contact counts are preserved
- Export options are still available

### Test 8: Clear Data Function

**Steps:**
1. Extract some contacts
2. Click "Clear All Data"
3. Confirm the action
4. Check the results

**Expected Results:**
- Confirmation dialog appears with details
- After confirmation, all data is cleared
- Contact counts reset to 0
- Export section disappears

## Error Testing

### Test 9: Network Issues

**Steps:**
1. Disconnect internet
2. Try to extract contacts
3. Reconnect and try again

**Expected Results:**
- Graceful error handling
- Appropriate error messages
- Recovery when connection restored

### Test 10: WhatsApp Web Changes

**Steps:**
1. Refresh WhatsApp Web while extension is open
2. Try extraction immediately after refresh
3. Wait for WhatsApp to fully load and try again

**Expected Results:**
- Extension handles page refreshes
- Shows appropriate status messages
- Works correctly after WhatsApp loads

## Performance Testing

### Test 11: Large Groups

**Steps:**
1. Open a large group (100+ members)
2. Extract group contacts
3. Monitor performance

**Expected Results:**
- Extraction completes without timeout
- No browser freezing
- Reasonable extraction time

### Test 12: Many Chats

**Steps:**
1. Have many chats visible (50+)
2. Extract chat contacts
3. Monitor performance

**Expected Results:**
- Handles large chat lists
- Extraction completes successfully
- Good performance

## Browser Console Testing

### Test 13: Console Errors

**Steps:**
1. Open browser console (F12)
2. Perform all main functions
3. Check for JavaScript errors

**Expected Results:**
- No critical errors in console
- Only expected log messages
- No uncaught exceptions

## Troubleshooting Common Issues

### Issue: "No contacts extracted"
**Possible Causes:**
- WhatsApp Web not fully loaded
- Group chat not open (for group extraction)
- Chat list not visible (for chat extraction)
- WhatsApp Web interface changes

**Solutions:**
- Refresh WhatsApp Web
- Make sure you're in the right view
- Try scrolling to load more chats
- Check browser console for errors

### Issue: "Export not working"
**Possible Causes:**
- Browser blocking downloads
- No contacts to export
- File permission issues

**Solutions:**
- Check browser download settings
- Extract contacts first
- Try different export format

### Issue: "Extension not loading"
**Possible Causes:**
- Missing icon files
- Manifest.json errors
- File permission issues

**Solutions:**
- Generate all required icons
- Check file structure
- Reload extension in chrome://extensions/

## Test Data Validation

After extraction, verify:
- Contact names are readable
- Phone numbers are in correct format
- No duplicate entries (if setting enabled)
- Export files open correctly in respective applications

## Reporting Issues

When reporting issues, include:
- Chrome version
- Extension version
- Steps to reproduce
- Expected vs actual behavior
- Browser console errors
- Screenshots if helpful
