# WA Contact Extractor

A Chrome extension that allows you to extract and export WhatsApp group member contacts and chat contacts with ease.

## Features

- **Extract Group Contacts**: Extract contact information from WhatsApp group members
- **Extract Chat Contacts**: Extract contacts from your WhatsApp chat list
- **Multiple Export Formats**: Export contacts in CSV, JSON, and TXT formats
- **Customizable Settings**: Choose what information to include (names, status, etc.)
- **Duplicate Removal**: Automatically remove duplicate contacts
- **Local Storage**: Safely store extracted contacts locally in your browser

## Installation

1. Clone or download this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right corner
4. Click "Load unpacked" and select the extension directory
5. The extension icon should appear in your Chrome toolbar

## Usage

1. Open [WhatsApp Web](https://web.whatsapp.com) in Chrome
2. Click on the WA Contact Extractor icon in your toolbar
3. Choose your extraction method:
   - **Extract Group Contacts**: Open a group chat first, then click this button
   - **Extract Chat Contacts**: Extract contacts from your chat list
4. Configure settings as needed (include names, status, remove duplicates)
5. Export your contacts in your preferred format (CSV, JSON, TXT)

## How It Works

The extension uses content scripts to interact with WhatsApp Web's DOM structure:

- **Group Extraction**: Opens group info panels and extracts member information
- **Chat Extraction**: Scans through your chat list to extract contact details
- **Phone Number Detection**: Attempts to extract phone numbers from various DOM elements
- **Safe Operation**: Only reads information, never modifies your WhatsApp data

## File Structure

```
├── manifest.json          # Extension configuration
├── popup.html             # Extension popup interface
├── popup.css              # Popup styling
├── popup.js               # Popup functionality
├── content.js             # WhatsApp Web interaction script
├── background.js          # Background service worker
├── icons/                 # Extension icons
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md              # This file
```

## Settings

- **Include contact names**: Extract and include contact names in exports
- **Include status messages**: Include last seen status or messages
- **Remove duplicate contacts**: Automatically filter out duplicate entries

## Export Formats

### CSV Format
```csv
Name,Phone,Status,Type
"John Doe","+1234567890","Online","group"
```

### JSON Format
```json
[
  {
    "name": "John Doe",
    "phone": "+1234567890",
    "status": "Online",
    "type": "group"
  }
]
```

### TXT Format
```
Name: John Doe | Phone: +1234567890 | Status: Online | Type: group
```

## Privacy & Security

- **No Data Collection**: This extension does not collect or transmit any personal data
- **Local Storage Only**: All extracted contacts are stored locally in your browser
- **Read-Only Access**: The extension only reads information from WhatsApp Web
- **No External Servers**: All processing happens locally on your device

## Limitations

- Only works with WhatsApp Web (not mobile app)
- Phone number extraction depends on WhatsApp's display of contact information
- Some contacts may not have visible phone numbers due to privacy settings
- Group extraction requires opening group info panels

## Troubleshooting

### "Please open WhatsApp Web first" Error
- Make sure you have WhatsApp Web open in a Chrome tab
- Ensure you're logged in to WhatsApp Web
- Refresh the WhatsApp Web page if needed

### No Contacts Extracted
- For group extraction: Make sure you're in a group chat and can see the member list
- For chat extraction: Ensure your chat list is loaded and visible
- Try scrolling through your chats to load more contacts

### Export Not Working
- Check that you have contacts extracted first
- Ensure Chrome has permission to download files
- Try a different export format

## Development

To modify or extend this extension:

1. Make changes to the relevant files
2. Reload the extension in `chrome://extensions/`
3. Test your changes on WhatsApp Web

### Key Components

- `content.js`: Handles DOM interaction with WhatsApp Web
- `popup.js`: Manages the extension popup interface
- `background.js`: Handles data storage and export functionality

## Disclaimer

This extension is not affiliated with or endorsed by WhatsApp Inc. It's an unofficial tool designed to help users manage their contacts. Please use responsibly and in accordance with WhatsApp's terms of service.

## License

This project is open source and available under the MIT License.

## Version History

- **v1.0.0**: Initial release with basic contact extraction and export functionality
