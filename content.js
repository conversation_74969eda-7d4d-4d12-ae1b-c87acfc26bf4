class WhatsAppContactExtractor {
    constructor() {
        this.contacts = [];
        this.init();
    }

    init() {
        // Listen for messages from popup
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'extractContacts') {
                this.handleExtractContacts(request, sendResponse);
                return true; // Keep message channel open for async response
            }
        });
    }

    async handleExtractContacts(request, sendResponse) {
        try {
            const { type, settings } = request;
            let contacts = [];

            if (type === 'group') {
                contacts = await this.extractGroupContacts(settings);
            } else if (type === 'chat') {
                contacts = await this.extractChatContacts(settings);
            }

            if (settings.removeDuplicates) {
                contacts = this.removeDuplicates(contacts);
            }

            sendResponse({ success: true, contacts });
        } catch (error) {
            console.error('Error in handleExtractContacts:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async extractGroupContacts(settings) {
        const contacts = [];

        // Wait for WhatsApp to load
        await this.waitForElement('[data-testid="chat-list"]');

        // Check if we're in a group chat by looking for group header
        const headerElement = document.querySelector('[data-testid="conversation-header"]');
        if (!headerElement) {
            throw new Error('Please open a chat first');
        }

        // Look for group indicators
        const groupTitle = headerElement.querySelector('[data-testid="conversation-title"]');
        const participantCount = headerElement.querySelector('[data-testid="conversation-subtitle"]');

        if (!participantCount || !participantCount.textContent.includes('participant')) {
            throw new Error('Please open a group chat first (individual chats not supported for group extraction)');
        }

        // Click on group header to open group info
        headerElement.click();

        // Wait for group info panel to load
        await this.waitForElement('[data-testid="drawer-right"]', 5000);

        // Look for "View all" or participant list
        const viewAllButton = document.querySelector('[data-testid="group-info-participants-view-all"]');
        if (viewAllButton) {
            viewAllButton.click();
            await this.sleep(1000);
        }

        // Extract group members from the participants list
        const participantSelectors = [
            '[data-testid="cell-frame-container"]',
            '[data-testid="participant-item"]',
            '.copyable-text[data-testid="conversation-title"]'
        ];

        let memberElements = [];
        for (const selector of participantSelectors) {
            memberElements = document.querySelectorAll(selector);
            if (memberElements.length > 0) break;
        }

        for (const memberElement of memberElements) {
            try {
                const contact = await this.extractContactFromElement(memberElement, 'group', settings);
                if (contact && contact.name) {
                    contacts.push(contact);
                }
            } catch (error) {
                console.warn('Error extracting contact from element:', error);
            }
        }

        // Close the group info panel
        const closeButton = document.querySelector('[data-testid="back"]') ||
                           document.querySelector('[data-testid="drawer-right"] [data-testid="x"]');
        if (closeButton) {
            closeButton.click();
            await this.sleep(500);
        }

        return contacts;
    }

    async extractChatContacts(settings) {
        const contacts = [];
        
        // Wait for chat list to load
        await this.waitForElement('[data-testid="chat-list"]');
        
        // Get all chat items
        const chatElements = document.querySelectorAll('[data-testid="cell-frame-container"]');
        
        for (const chatElement of chatElements) {
            try {
                const contact = await this.extractContactFromElement(chatElement, 'chat', settings);
                if (contact) {
                    contacts.push(contact);
                }
            } catch (error) {
                console.warn('Error extracting contact from chat element:', error);
            }
        }

        return contacts;
    }

    async extractContactFromElement(element, type, settings) {
        const contact = { type };

        // Extract name with multiple fallback strategies
        if (settings.includeNames) {
            const nameSelectors = [
                '[data-testid="conversation-title"]',
                '[data-testid="cell-frame-title"]',
                '.copyable-text[data-testid="conversation-title"]',
                'span[title][dir="auto"]',
                'span[dir="auto"]',
                '.copyable-text'
            ];

            for (const selector of nameSelectors) {
                const nameElement = element.querySelector(selector);
                if (nameElement && nameElement.textContent.trim()) {
                    contact.name = nameElement.textContent.trim();
                    break;
                }
            }

            // Try to get name from title attribute
            if (!contact.name) {
                const titleElements = element.querySelectorAll('[title]');
                for (const titleElement of titleElements) {
                    const title = titleElement.title.trim();
                    if (title && !title.includes('+') && title.length > 0) {
                        contact.name = title;
                        break;
                    }
                }
            }
        }

        // Extract phone number with improved detection
        const phoneNumber = this.extractPhoneNumber(element, contact.name);
        if (phoneNumber) {
            contact.phone = phoneNumber;
        }

        // Extract status/last message with better selectors
        if (settings.includeStatus) {
            const statusSelectors = [
                '[data-testid="last-msg-status"]',
                '[data-testid="cell-frame-secondary"]',
                '.copyable-text[data-testid="last-msg-status"]',
                'span[title]:not([data-testid="conversation-title"])'
            ];

            for (const selector of statusSelectors) {
                const statusElement = element.querySelector(selector);
                if (statusElement && statusElement.textContent.trim()) {
                    const statusText = statusElement.textContent.trim();
                    // Avoid using phone numbers as status
                    if (!statusText.match(/^\+?[\d\s\-\(\)]+$/)) {
                        contact.status = statusText;
                        break;
                    }
                }
            }
        }

        // Enhanced validation - require at least name or phone
        if (contact.name || contact.phone) {
            // Clean up the contact data
            if (contact.name) {
                contact.name = contact.name.replace(/\s+/g, ' ').trim();
            }
            if (contact.status) {
                contact.status = contact.status.replace(/\s+/g, ' ').trim();
            }
            return contact;
        }

        return null;
    }

    extractPhoneNumber(element, name) {
        // Enhanced phone number extraction with multiple strategies
        const phonePatterns = [
            /\+\d{1,4}[\s\-]?\d{1,4}[\s\-]?\d{1,4}[\s\-]?\d{1,9}/g,  // International format
            /\+\d{10,15}/g,  // Simple international
            /\d{10,15}/g     // Local format (10+ digits)
        ];

        // Strategy 1: Look for elements with phone-like content
        const phoneSelectors = [
            '[title*="+"]',
            '[data-testid*="phone"]',
            'span[dir="ltr"]',
            '.copyable-text',
            'span[title]'
        ];

        for (const selector of phoneSelectors) {
            const elements = element.querySelectorAll(selector);
            for (const phoneElement of elements) {
                const sources = [
                    phoneElement.textContent,
                    phoneElement.title,
                    phoneElement.getAttribute('data-phone'),
                    phoneElement.getAttribute('aria-label')
                ].filter(Boolean);

                for (const source of sources) {
                    for (const pattern of phonePatterns) {
                        const matches = source.match(pattern);
                        if (matches) {
                            for (const match of matches) {
                                const cleaned = match.replace(/[\s\-\(\)]/g, '');
                                // Validate phone number length and format
                                if (cleaned.length >= 10 && cleaned.length <= 15) {
                                    return cleaned;
                                }
                            }
                        }
                    }
                }
            }
        }

        // Strategy 2: Look in all text content for phone patterns
        const allText = element.textContent || '';
        for (const pattern of phonePatterns) {
            const matches = allText.match(pattern);
            if (matches) {
                for (const match of matches) {
                    const cleaned = match.replace(/[\s\-\(\)]/g, '');
                    if (cleaned.length >= 10 && cleaned.length <= 15) {
                        // Make sure it's not part of the name
                        if (!name || !name.includes(match)) {
                            return cleaned;
                        }
                    }
                }
            }
        }

        // Strategy 3: Check for WhatsApp's contact format in onclick or data attributes
        const clickableElements = element.querySelectorAll('[onclick], [data-id], [href]');
        for (const clickElement of clickableElements) {
            const attributes = [
                clickElement.getAttribute('onclick'),
                clickElement.getAttribute('data-id'),
                clickElement.getAttribute('href')
            ].filter(Boolean);

            for (const attr of attributes) {
                for (const pattern of phonePatterns) {
                    const matches = attr.match(pattern);
                    if (matches) {
                        const cleaned = matches[0].replace(/[\s\-\(\)]/g, '');
                        if (cleaned.length >= 10 && cleaned.length <= 15) {
                            return cleaned;
                        }
                    }
                }
            }
        }

        return null;
    }

    removeDuplicates(contacts) {
        const seen = new Set();
        return contacts.filter(contact => {
            const key = contact.phone || contact.name;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            // Check if element already exists
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }

            let attempts = 0;
            const maxAttempts = Math.floor(timeout / 100);

            const checkElement = () => {
                attempts++;
                const element = document.querySelector(selector);

                if (element) {
                    resolve(element);
                    return;
                }

                if (attempts >= maxAttempts) {
                    reject(new Error(`Element ${selector} not found within ${timeout}ms after ${attempts} attempts`));
                    return;
                }

                setTimeout(checkElement, 100);
            };

            // Start checking
            setTimeout(checkElement, 100);
        });
    }

    waitForElementWithFallback(selectors, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const checkSelectors = () => {
                for (const selector of selectors) {
                    const element = document.querySelector(selector);
                    if (element) {
                        resolve({ element, selector });
                        return true;
                    }
                }
                return false;
            };

            // Check immediately
            if (checkSelectors()) return;

            let attempts = 0;
            const maxAttempts = Math.floor(timeout / 100);

            const checkLoop = () => {
                attempts++;

                if (checkSelectors()) return;

                if (attempts >= maxAttempts) {
                    reject(new Error(`None of the selectors found within ${timeout}ms: ${selectors.join(', ')}`));
                    return;
                }

                setTimeout(checkLoop, 100);
            };

            setTimeout(checkLoop, 100);
        });
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize the content script
new WhatsAppContactExtractor();
