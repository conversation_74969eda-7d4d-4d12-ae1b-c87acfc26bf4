* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 350px;
    min-height: 400px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
    color: #333;
}

.container {
    padding: 20px;
}

.header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    color: white;
}

.logo {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
}

.status {
    background: rgba(255, 255, 255, 0.9);
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
}

.status.loading {
    background: rgba(255, 193, 7, 0.9);
    color: #856404;
}

.status.success {
    background: rgba(40, 167, 69, 0.9);
    color: white;
}

.status.error {
    background: rgba(220, 53, 69, 0.9);
    color: white;
}

.main-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: white;
    color: #128C7E;
}

.btn-primary:hover {
    background: #f8f9fa;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-small {
    padding: 8px 12px;
    font-size: 12px;
}

.btn-icon {
    font-size: 16px;
}

.contacts-info {
    background: rgba(255, 255, 255, 0.9);
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: 500;
}

.info-value {
    font-weight: 600;
    color: #128C7E;
}

.export-section {
    background: rgba(255, 255, 255, 0.1);
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.export-section h3 {
    color: white;
    font-size: 16px;
    margin-bottom: 12px;
}

.export-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.settings-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-bottom: 20px;
}

.settings-section details {
    padding: 16px;
}

.settings-section summary {
    color: white;
    font-weight: 500;
    cursor: pointer;
    margin-bottom: 12px;
}

.setting-item {
    margin-bottom: 8px;
}

.setting-item label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    font-size: 14px;
    cursor: pointer;
}

.setting-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.version {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
}

.hidden {
    display: none !important;
}

.instructions-panel, .tips-panel {
    background: rgba(255, 255, 255, 0.95);
    padding: 16px;
    border-radius: 8px;
    margin-top: 20px;
    border-left: 4px solid #25D366;
}

.instructions-panel h4, .tips-panel h4 {
    margin: 0 0 12px 0;
    color: #128C7E;
    font-size: 14px;
}

.instructions-panel ol, .tips-panel ul {
    margin: 0;
    padding-left: 20px;
}

.instructions-panel li, .tips-panel li {
    margin-bottom: 8px;
    font-size: 13px;
    line-height: 1.4;
}

.instructions-panel a {
    color: #25D366;
    text-decoration: none;
}

.instructions-panel a:hover {
    text-decoration: underline;
}

.tips-panel {
    border-left-color: #ffc107;
}

.tips-panel h4 {
    color: #856404;
}
