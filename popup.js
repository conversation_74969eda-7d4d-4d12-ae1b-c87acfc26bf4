class WAContactExtractor {
    constructor() {
        this.contacts = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadStoredContacts();
        this.checkWhatsAppTab();
    }

    bindEvents() {
        document.getElementById('extractGroupContacts').addEventListener('click', () => {
            this.extractContacts('group');
        });

        document.getElementById('extractChatContacts').addEventListener('click', () => {
            this.extractContacts('chat');
        });

        document.getElementById('exportCSV').addEventListener('click', () => {
            this.exportContacts('csv');
        });

        document.getElementById('exportJSON').addEventListener('click', () => {
            this.exportContacts('json');
        });

        document.getElementById('exportTXT').addEventListener('click', () => {
            this.exportContacts('txt');
        });

        document.getElementById('exportXLSX').addEventListener('click', () => {
            this.exportContacts('xlsx');
        });

        document.getElementById('clearData').addEventListener('click', () => {
            this.clearAllData();
        });
    }

    async checkWhatsAppTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!tab.url.includes('web.whatsapp.com')) {
                this.updateStatus('⚠️ Please open WhatsApp Web first', 'error');
                this.disableButtons();
                this.showWhatsAppInstructions();
            } else {
                this.updateStatus('✅ Ready to extract contacts', 'success');
            }
        } catch (error) {
            console.error('Error checking tab:', error);
            this.updateStatus('❌ Error checking WhatsApp Web', 'error');
        }
    }

    showWhatsAppInstructions() {
        const instructionsHtml = `
            <div class="instructions-panel">
                <h4>📱 How to get started:</h4>
                <ol>
                    <li>Open <a href="https://web.whatsapp.com" target="_blank">WhatsApp Web</a></li>
                    <li>Log in with your phone</li>
                    <li>Come back to this extension</li>
                </ol>
            </div>
        `;

        const container = document.querySelector('.container');
        const existingInstructions = container.querySelector('.instructions-panel');
        if (!existingInstructions) {
            container.insertAdjacentHTML('beforeend', instructionsHtml);
        }
    }

    disableButtons() {
        const buttons = document.querySelectorAll('.btn-primary');
        buttons.forEach(btn => {
            btn.disabled = true;
            btn.style.opacity = '0.5';
            btn.style.cursor = 'not-allowed';
        });
    }

    async extractContacts(type) {
        const typeLabel = type === 'group' ? 'group' : 'chat';
        this.updateStatus(`🔍 Extracting ${typeLabel} contacts...`, 'loading');

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab.url.includes('web.whatsapp.com')) {
                this.updateStatus('❌ Please open WhatsApp Web first', 'error');
                return;
            }

            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'extractContacts',
                type: type,
                settings: this.getSettings()
            });

            if (response.success) {
                this.contacts = response.contacts;
                this.updateContactsDisplay();
                this.saveContacts();

                const phoneCount = this.contacts.filter(c => c.phone).length;
                this.updateStatus(
                    `✅ Extracted ${this.contacts.length} contacts (${phoneCount} with phone numbers)`,
                    'success'
                );

                // Remove instructions panel if it exists
                const instructions = document.querySelector('.instructions-panel');
                if (instructions) {
                    instructions.remove();
                }
            } else {
                this.updateStatus(`❌ ${response.error || 'Failed to extract contacts'}`, 'error');
                this.showExtractionTips(type);
            }
        } catch (error) {
            console.error('Error extracting contacts:', error);
            this.updateStatus('❌ Error extracting contacts', 'error');
            this.showExtractionTips(type);
        }
    }

    showExtractionTips(type) {
        const tips = type === 'group'
            ? ['Open a group chat first', 'Make sure you can see group members', 'Try refreshing WhatsApp Web']
            : ['Make sure your chat list is visible', 'Try scrolling through your chats', 'Refresh WhatsApp Web if needed'];

        const tipsHtml = `
            <div class="tips-panel">
                <h4>💡 Extraction Tips:</h4>
                <ul>
                    ${tips.map(tip => `<li>${tip}</li>`).join('')}
                </ul>
            </div>
        `;

        const container = document.querySelector('.container');
        const existingTips = container.querySelector('.tips-panel');
        if (!existingTips) {
            container.insertAdjacentHTML('beforeend', tipsHtml);
        }
    }

    getSettings() {
        return {
            includeNames: document.getElementById('includeNames').checked,
            includeStatus: document.getElementById('includeStatus').checked,
            removeDuplicates: document.getElementById('removeDuplicates').checked
        };
    }

    updateContactsDisplay() {
        const contactsInfo = document.getElementById('contactsInfo');
        const exportSection = document.getElementById('exportSection');
        
        if (this.contacts.length > 0) {
            contactsInfo.style.display = 'block';
            exportSection.style.display = 'block';
            
            document.getElementById('totalContacts').textContent = this.contacts.length;
            document.getElementById('phoneContacts').textContent = 
                this.contacts.filter(c => c.phone).length;
        } else {
            contactsInfo.style.display = 'none';
            exportSection.style.display = 'none';
        }
    }

    async exportContacts(format) {
        if (this.contacts.length === 0) {
            this.updateStatus('No contacts to export', 'error');
            return;
        }

        try {
            let content, filename, mimeType;

            switch (format) {
                case 'csv':
                    content = this.generateCSV();
                    filename = `whatsapp_contacts_${Date.now()}.csv`;
                    mimeType = 'text/csv';
                    break;
                case 'json':
                    content = JSON.stringify(this.contacts, null, 2);
                    filename = `whatsapp_contacts_${Date.now()}.json`;
                    mimeType = 'application/json';
                    break;
                case 'txt':
                    content = this.generateTXT();
                    filename = `whatsapp_contacts_${Date.now()}.txt`;
                    mimeType = 'text/plain';
                    break;
                case 'xlsx':
                    content = this.generateXLSX();
                    filename = `whatsapp_contacts_${Date.now()}.xlsx`;
                    mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                    break;
            }

            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            
            await chrome.downloads.download({
                url: url,
                filename: filename
            });

            this.updateStatus(`Exported ${this.contacts.length} contacts as ${format.toUpperCase()}`, 'success');
        } catch (error) {
            console.error('Error exporting contacts:', error);
            this.updateStatus('Error exporting contacts', 'error');
        }
    }

    generateCSV() {
        const headers = ['Name', 'Phone', 'Status', 'Type'];
        const rows = [headers.join(',')];
        
        this.contacts.forEach(contact => {
            const row = [
                `"${contact.name || ''}"`,
                `"${contact.phone || ''}"`,
                `"${contact.status || ''}"`,
                `"${contact.type || ''}"`
            ];
            rows.push(row.join(','));
        });
        
        return rows.join('\n');
    }

    generateTXT() {
        return this.contacts.map(contact => {
            const parts = [];
            if (contact.name) parts.push(`Name: ${contact.name}`);
            if (contact.phone) parts.push(`Phone: ${contact.phone}`);
            if (contact.status) parts.push(`Status: ${contact.status}`);
            if (contact.type) parts.push(`Type: ${contact.type}`);
            return parts.join(' | ');
        }).join('\n');
    }

    generateXLSX() {
        // Simple XLSX generation using XML structure
        const headers = ['Name', 'Phone', 'Status', 'Type'];
        const rows = this.contacts.map(contact => [
            contact.name || '',
            contact.phone || '',
            contact.status || '',
            contact.type || ''
        ]);

        // Create basic XLSX structure
        const worksheet = this.createWorksheet(headers, rows);
        const workbook = this.createWorkbook(worksheet);

        return workbook;
    }

    createWorksheet(headers, rows) {
        let xml = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>';
        xml += '<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">';
        xml += '<sheetData>';

        // Add header row
        xml += '<row r="1">';
        headers.forEach((header, index) => {
            const cellRef = String.fromCharCode(65 + index) + '1';
            xml += `<c r="${cellRef}" t="inlineStr"><is><t>${this.escapeXml(header)}</t></is></c>`;
        });
        xml += '</row>';

        // Add data rows
        rows.forEach((row, rowIndex) => {
            const rowNum = rowIndex + 2;
            xml += `<row r="${rowNum}">`;
            row.forEach((cell, colIndex) => {
                const cellRef = String.fromCharCode(65 + colIndex) + rowNum;
                xml += `<c r="${cellRef}" t="inlineStr"><is><t>${this.escapeXml(cell)}</t></is></c>`;
            });
            xml += '</row>';
        });

        xml += '</sheetData></worksheet>';
        return xml;
    }

    createWorkbook(worksheet) {
        // This is a simplified XLSX format - for full compatibility, use a proper library
        // But this should work for basic Excel import
        const zip = this.createZipStructure(worksheet);
        return zip;
    }

    createZipStructure(worksheet) {
        // For simplicity, we'll create a CSV-like format that Excel can import
        // In a real implementation, you'd use a ZIP library to create proper XLSX
        const headers = ['Name', 'Phone', 'Status', 'Type'];
        const rows = [headers.join('\t')];

        this.contacts.forEach(contact => {
            const row = [
                contact.name || '',
                contact.phone || '',
                contact.status || '',
                contact.type || ''
            ];
            rows.push(row.join('\t'));
        });

        return rows.join('\n');
    }

    escapeXml(text) {
        return String(text)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&apos;');
    }

    async saveContacts() {
        try {
            const timestamp = Date.now();
            const storageData = {
                contacts: this.contacts,
                lastUpdated: timestamp,
                settings: this.getSettings()
            };

            await chrome.storage.local.set(storageData);

            // Also save to session storage for backup
            await chrome.storage.session.set({
                contactsBackup: this.contacts,
                backupTimestamp: timestamp
            });

            console.log(`Saved ${this.contacts.length} contacts to storage`);
        } catch (error) {
            console.error('Error saving contacts:', error);
            this.updateStatus('⚠️ Error saving contacts', 'error');
        }
    }

    async loadStoredContacts() {
        try {
            // Try to load from local storage first
            const result = await chrome.storage.local.get(['contacts', 'lastUpdated', 'settings']);

            if (result.contacts && result.contacts.length > 0) {
                this.contacts = result.contacts;
                this.updateContactsDisplay();

                // Update settings if they exist
                if (result.settings) {
                    this.applySettings(result.settings);
                }

                // Show last updated time
                if (result.lastUpdated) {
                    const lastUpdated = new Date(result.lastUpdated).toLocaleString();
                    console.log(`Loaded ${this.contacts.length} contacts (last updated: ${lastUpdated})`);
                }

                return;
            }

            // Fallback to session storage backup
            const sessionResult = await chrome.storage.session.get(['contactsBackup', 'backupTimestamp']);
            if (sessionResult.contactsBackup && sessionResult.contactsBackup.length > 0) {
                this.contacts = sessionResult.contactsBackup;
                this.updateContactsDisplay();

                // Save to local storage
                await this.saveContacts();

                console.log(`Restored ${this.contacts.length} contacts from backup`);
            }
        } catch (error) {
            console.error('Error loading contacts:', error);
        }
    }

    applySettings(settings) {
        try {
            document.getElementById('includeNames').checked = settings.includeNames !== false;
            document.getElementById('includeStatus').checked = settings.includeStatus !== false;
            document.getElementById('removeDuplicates').checked = settings.removeDuplicates !== false;
        } catch (error) {
            console.error('Error applying settings:', error);
        }
    }

    async getStorageInfo() {
        try {
            const result = await chrome.storage.local.get(null);
            const storageSize = JSON.stringify(result).length;
            const contactCount = result.contacts ? result.contacts.length : 0;

            return {
                contactCount,
                storageSize,
                lastUpdated: result.lastUpdated
            };
        } catch (error) {
            console.error('Error getting storage info:', error);
            return { contactCount: 0, storageSize: 0, lastUpdated: null };
        }
    }

    async clearAllData() {
        const storageInfo = await this.getStorageInfo();
        const confirmMessage = `Are you sure you want to clear all data?\n\n` +
                              `This will delete:\n` +
                              `• ${storageInfo.contactCount} extracted contacts\n` +
                              `• All saved settings\n` +
                              `• Storage backup\n\n` +
                              `This action cannot be undone.`;

        if (confirm(confirmMessage)) {
            try {
                // Clear all storage
                await chrome.storage.local.clear();
                await chrome.storage.session.clear();

                // Reset local state
                this.contacts = [];
                this.updateContactsDisplay();

                // Remove any instruction/tip panels
                const panels = document.querySelectorAll('.instructions-panel, .tips-panel');
                panels.forEach(panel => panel.remove());

                this.updateStatus('🗑️ All data cleared successfully', 'success');

                // Reset to initial state
                setTimeout(() => {
                    this.updateStatus('✅ Ready to extract contacts', '');
                }, 2000);

            } catch (error) {
                console.error('Error clearing data:', error);
                this.updateStatus('❌ Error clearing data', 'error');
            }
        }
    }

    updateStatus(message, type = '') {
        const statusElement = document.getElementById('status');
        const statusText = statusElement.querySelector('.status-text');
        
        statusText.textContent = message;
        statusElement.className = `status ${type}`;
        
        if (type === 'loading') {
            statusText.textContent += '...';
        }
    }
}

// Initialize the extension when popup loads
document.addEventListener('DOMContentLoaded', () => {
    new WAContactExtractor();
});
