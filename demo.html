<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WA Contact Extractor - Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #128C7E;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            color: #666;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #25D366;
        }
        
        .demo-section h2 {
            color: #128C7E;
            margin-top: 0;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .feature-card h3 {
            color: #128C7E;
            margin-top: 0;
        }
        
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .screenshot {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            margin: 20px 0;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            margin: 20px 0;
        }
        
        .install-steps {
            counter-reset: step-counter;
        }
        
        .install-step {
            counter-increment: step-counter;
            margin-bottom: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            position: relative;
            padding-left: 60px;
        }
        
        .install-step::before {
            content: counter(step-counter);
            position: absolute;
            left: 20px;
            top: 20px;
            background: #25D366;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .cta-section {
            text-align: center;
            background: linear-gradient(135deg, #25D366, #128C7E);
            color: white;
            padding: 40px;
            border-radius: 12px;
            margin-top: 40px;
        }
        
        .cta-button {
            display: inline-block;
            background: white;
            color: #128C7E;
            padding: 15px 30px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            margin: 10px;
            transition: transform 0.2s;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 WA Contact Extractor</h1>
            <p>Extract and export WhatsApp contacts with ease</p>
        </div>

        <div class="demo-section">
            <h2>✨ Key Features</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">👥</div>
                    <h3>Group Contact Extraction</h3>
                    <p>Extract contact information from WhatsApp group members with a single click.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">💬</div>
                    <h3>Chat Contact Extraction</h3>
                    <p>Extract contacts from your WhatsApp chat list quickly and efficiently.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>Multiple Export Formats</h3>
                    <p>Export your contacts in CSV, JSON, TXT, or XLSX formats for maximum compatibility.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔒</div>
                    <h3>Privacy First</h3>
                    <p>All data stays local on your device. No external servers or data collection.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⚙️</div>
                    <h3>Customizable Settings</h3>
                    <p>Choose what information to include and how to format your exports.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🗑️</div>
                    <h3>Smart Duplicate Removal</h3>
                    <p>Automatically remove duplicate contacts to keep your exports clean.</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>📋 How It Works</h2>
            <div class="install-steps">
                <div class="install-step">
                    <h3>Install the Extension</h3>
                    <p>Load the unpacked extension in Chrome's developer mode. Generate the required icon files using our icon generator.</p>
                </div>
                
                <div class="install-step">
                    <h3>Open WhatsApp Web</h3>
                    <p>Navigate to <a href="https://web.whatsapp.com" target="_blank">web.whatsapp.com</a> and log in with your phone.</p>
                </div>
                
                <div class="install-step">
                    <h3>Extract Contacts</h3>
                    <p>Click the extension icon and choose to extract either group contacts or chat contacts.</p>
                </div>
                
                <div class="install-step">
                    <h3>Export Your Data</h3>
                    <p>Choose your preferred export format and download your contact list.</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>📄 Sample Export Formats</h2>
            
            <h3>CSV Format</h3>
            <div class="code-block">
Name,Phone,Status,Type
"John Doe","+1234567890","Online","group"
"Jane Smith","+0987654321","Last seen recently","chat"
            </div>
            
            <h3>JSON Format</h3>
            <div class="code-block">
[
  {
    "name": "John Doe",
    "phone": "+1234567890",
    "status": "Online",
    "type": "group"
  },
  {
    "name": "Jane Smith",
    "phone": "+0987654321",
    "status": "Last seen recently",
    "type": "chat"
  }
]
            </div>
        </div>

        <div class="demo-section">
            <h2>⚠️ Important Notes</h2>
            <div class="warning">
                <strong>Privacy & Legal:</strong> This extension is for personal use only. Respect others' privacy and comply with WhatsApp's terms of service. Only extract contacts you have permission to access.
            </div>
            
            <ul>
                <li><strong>WhatsApp Web Only:</strong> This extension only works with WhatsApp Web, not the mobile app</li>
                <li><strong>Phone Number Visibility:</strong> Phone numbers are only extracted if they're visible in WhatsApp Web</li>
                <li><strong>Group Access:</strong> You must be a member of groups to extract their contacts</li>
                <li><strong>No External Data:</strong> All processing happens locally on your device</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>🛠️ Technical Details</h2>
            <p>The extension uses:</p>
            <ul>
                <li><strong>Content Scripts:</strong> To interact with WhatsApp Web's DOM</li>
                <li><strong>Chrome Storage API:</strong> To save extracted contacts locally</li>
                <li><strong>Downloads API:</strong> To export contact files</li>
                <li><strong>Manifest V3:</strong> Latest Chrome extension standard</li>
            </ul>
        </div>

        <div class="cta-section">
            <h2>🎯 Ready to Get Started?</h2>
            <p>Extract your WhatsApp contacts in minutes!</p>
            <a href="https://web.whatsapp.com" target="_blank" class="cta-button">Open WhatsApp Web</a>
            <a href="generate-icons.html" class="cta-button">Generate Icons</a>
        </div>
    </div>
</body>
</html>
